# 💪 Trainify
A comprehensive workout tracking application built with React, TypeScript, and Tailwind CSS.

## ✨ Features
- **🔐 User Authentication**: Sign up and log in to personalize the workout experience.
- **📚 Exercise Library**: Browse a comprehensive collection of exercises organized by body parts.
- **📋 Detailed Exercise Information**: View step-by-step instructions and animations for proper form.
- **📝 Workout Tracking**: Create, edit, and delete workout sessions.
- **📊 Exercise Logging**: Track sets, reps, weight, and notes for each exercise.
- **📱 Responsive Design**: Works seamlessly on both desktop and mobile devices.

## 🛠️ Technologies Used
- **⚛️ React**: Frontend library for building user interfaces.
- **🔷 TypeScript**: Static typing for JavaScript.
- **🎨 Tailwind CSS**: Utility-first CSS framework.
- **🧭 React Router**: Navigation and routing.
- **🎯 Lucide React**: Icon library.
- **🧠 Context API**: State management.
- **💾 Local Storage**: Data persistence.

## 🚀 Getting Started
### Prerequisites
- 📦 Node.js (v14 or higher)
- 📦 npm or yarn

### Installation
1. Clone the repository:
```bash
git clone https://github.com/yourusername/trainify.git
cd trainify
```
2. Install Dependencies:
```bash
npm install
```
3. Start the development server:
```bash
npm run dev
# or
yarn dev
```
4. Open your browser and navigate to [http://localhost:3000](http://localhost:3000).

## 📁 Project Structure
- `src/` 
  - `components/` - Reusable UI components
  - `context/` - React Context for state management
  - `data/` - Mock data for exercises and workouts
  - `pages/` - Application pages
  - `types/` - TypeScript type definitions
  - `App.tsx` - Main application component
  - `main.tsx` - Application entry point

## 📸 Project Screenshots

### Home Page
![image](https://github.com/user-attachments/assets/8b0061fc-2bed-465e-9cc6-b35503dc4fdc)

### Home Dashboard
![image](https://github.com/user-attachments/assets/166be45f-1444-4f70-bf80-60b91b955db2)
![image](https://github.com/user-attachments/assets/b98ae238-92af-4c93-a7fb-c9e561cdf68e)
![image](https://github.com/user-attachments/assets/3ff0217d-0efe-4621-9e61-63535239d282)

### Exercise Details by Body Part
![image](https://github.com/user-attachments/assets/6753cf60-e112-4153-8a56-dd891b6df01b)

### Body-Part-Specific Exercises
![image](https://github.com/user-attachments/assets/26e57cdf-8ab1-4afb-be2f-c39bb55effd4)

### Adding a Workout
![image](https://github.com/user-attachments/assets/e7f6242c-40ff-4e37-9193-437c365e43a5)

### Tracking Workouts
1. You can add a workout.
2. You can edit or delete workouts based on your activity.
![image](https://github.com/user-attachments/assets/********-b9de-4b9b-a8c6-33ac2a16da2f)

## 📖 Usage
1. 🔑 **Sign Up/Login**: Create an account or log in to access the dashboard.
2. 🔍 **Browse Exercises**: Explore exercises categorized by body part.
3. ℹ️ **View Exercise Details**: Click on an exercise to see step-by-step instructions and animations.
4. 📝 **Track Workouts**: Add, edit, and delete your workout sessions.
5. 📊 **Log Exercises**: Record sets, reps, weight, and notes for each exercise.

## 🔮 Future Enhancements
1. 📈 Progress tracking with charts and statistics.
2. 📋 Predefined workout plan templates.
3. 🌐 Social sharing features.
4. ⌚ Integration with fitness wearables.
5. 🥗 Nutrition tracking.

## 📜 License
This project is licensed under the MIT License - see the LICENSE file for details.

## 👨‍💻 Author
**Tarang Bhargava**
